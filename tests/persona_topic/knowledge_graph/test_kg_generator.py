import pytest
import json
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime

from src.persona_topic.knowledge_graph_system_prompt.generator import KnowledgeGraphSystemPromptGenerator
from src.persona_topic.knowledge_graph_system_prompt.settings import KnowledgeGraphSystemPromptGeneratorSettings
from src.persona_topic.knowledge_graph_system_prompt.models import Entity, Relationship, KnowledgeGraph, SubGraph
from src.persona_topic.knowledge_graph_system_prompt.gap_filling_models import Gap<PERSON><PERSON>ingResult, IslandResult
from src.persona_topic.models import PersonaTopic, DataGenSource


class TestKnowledgeGraphSystemPromptGenerator:
    """Test cases for the KnowledgeGraphSystemPromptGenerator class."""

    @pytest.fixture
    def default_settings(self):
        """Create default settings for testing."""
        return KnowledgeGraphSystemPromptGeneratorSettings()

    @pytest.fixture
    def custom_settings(self):
        """Create custom settings for testing."""
        return KnowledgeGraphSystemPromptGeneratorSettings(
            max_personas=10,
            max_topics_per_persona=8,
            max_tokens=1000,
            temperature=0.5,
            model="gpt-4",
            include_context=False,
            additional_settings={"custom_key": "custom_value"}
        )

    @pytest.fixture
    def sample_knowledge_graph(self):
        """Create a sample knowledge graph for testing."""
        entities = [
            Entity(id="Python", type="programming_language", properties={"popularity": "high"}),
            Entity(id="Django", type="framework", properties={"language": "Python"}),
            Entity(id="Web Development", type="domain", properties={"complexity": "medium"})
        ]
        
        relationships = [
            Relationship(source_id="Django", target_id="Python", type="built_with"),
            Relationship(source_id="Django", target_id="Web Development", type="used_for")
        ]
        
        return KnowledgeGraph(
            entities=entities,
            relationships=relationships,
            metadata={"role": "developer", "domain": "web_development"}
        )

    @pytest.fixture
    def sample_personas(self):
        """Create sample personas for testing."""
        return [
            """
            Role: Python Developer
            
            Topics of interest:
            - Web frameworks like Django and Flask
            - Database integration and ORM usage
            - API development and REST services
            
            Simulating Style:
            Professional and technical
            """,
            """
            Role: Frontend Developer
            
            Topics of Interest:
            - JavaScript frameworks and libraries
            - CSS styling and responsive design
            - User experience optimization
            
            Simulating Style:
            Creative and user-focused
            """
        ]

    def test_generator_initialization_default_settings(self, default_settings):
        """Test generator initialization with default settings."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        assert generator.settings == default_settings
        assert generator.metadata == {}
        assert generator.completion_params["temperature"] == 0.7
        assert generator.completion_params["max_tokens"] == 500

    def test_generator_initialization_custom_settings(self, custom_settings):
        """Test generator initialization with custom settings."""
        metadata = {"experiment_id": "test_123"}
        generator = KnowledgeGraphSystemPromptGenerator(custom_settings, metadata)
        
        assert generator.settings == custom_settings
        assert generator.metadata == metadata
        assert generator.completion_params["temperature"] == 0.5
        assert generator.completion_params["max_tokens"] == 1000

    def test_extract_topics_section_basic(self, default_settings):
        """Test basic topic section extraction."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        persona = """
        Role: Developer
        
        Topics of interest:
        - Python programming
        - Web development
        - Database design
        
        Simulating Style:
        Professional
        """
        
        result = generator.extract_topics_section(persona)
        # The function includes the header, so we check for content presence
        assert "- Python programming" in result
        assert "- Web development" in result
        assert "- Database design" in result

    def test_extract_topics_section_with_examples(self, default_settings):
        """Test topic section extraction with examples header."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        persona = """
        Role: Developer
        
        Topics of interest (with examples):
        - Python programming: Django, Flask
        - Web development: HTML, CSS, JavaScript
        
        Simulating Style:
        Professional
        """
        
        result = generator.extract_topics_section(persona)
        assert "Python programming: Django, Flask" in result
        assert "Web development: HTML, CSS, JavaScript" in result
        assert "Topics of interest (with examples):" not in result

    def test_extract_topics_section_no_topics(self, default_settings):
        """Test topic section extraction when no topics section exists."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        persona = """
        Role: Developer
        
        Background:
        Experienced in software development
        
        Simulating Style:
        Professional
        """
        
        result = generator.extract_topics_section(persona)
        assert result == ""

    def test_extract_topics_section_no_simulating_style(self, default_settings):
        """Test topic section extraction when no simulating style section exists."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        persona = """
        Role: Developer

        Topics of interest:
        - Python programming
        - Web development
        """

        result = generator.extract_topics_section(persona)
        assert "Python programming" in result
        assert "Web development" in result

    def test_replace_topics_in_persona_basic(self, default_settings):
        """Test basic topic replacement in a persona."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        original_persona = """Role: Python Developer

Background: Experienced software engineer with 5 years in web development

Topics of interest:
- Web frameworks like Django and Flask
- Database integration and ORM usage
- API development and REST services

Simulating Style:
Professional and technical"""

        new_topic = "Machine learning integration with web applications"

        result = generator.replace_topics_in_persona(original_persona, new_topic)

        # Check that new topic is present
        assert new_topic in result
        # Check that old topics are removed
        assert "Web frameworks like Django and Flask" not in result
        assert "Database integration and ORM usage" not in result
        assert "API development and REST services" not in result
        # Check that other sections are preserved
        assert "Role: Python Developer" in result
        assert "Background: Experienced software engineer" in result
        assert "Simulating Style:" in result
        assert "Professional and technical" in result

    def test_replace_topics_in_persona_no_existing_topics(self, default_settings):
        """Test topic replacement when persona has no existing topics section."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        original_persona = """Role: Frontend Developer

Background: Creative developer focused on user experience

Simulating Style:
Creative and user-focused"""

        new_topic = "Accessibility in modern web design"

        result = generator.replace_topics_in_persona(original_persona, new_topic)

        # Check that topics section was added
        assert "Topics of interest:" in result
        assert new_topic in result
        # Check that other sections are preserved
        assert "Role: Frontend Developer" in result
        assert "Background: Creative developer" in result
        assert "Simulating Style:" in result
        assert "Creative and user-focused" in result

    def test_replace_topics_in_persona_no_simulating_style(self, default_settings):
        """Test topic replacement when persona has no simulating style section."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        original_persona = """Role: Data Scientist

Background: PhD in Statistics with industry experience

Topics of interest:
- Statistical modeling and analysis
- Machine learning algorithms
- Data visualization techniques"""

        new_topic = "Deep learning for natural language processing"

        result = generator.replace_topics_in_persona(original_persona, new_topic)

        # Check that new topic replaced old ones
        assert new_topic in result
        assert "Statistical modeling and analysis" not in result
        assert "Machine learning algorithms" not in result
        assert "Data visualization techniques" not in result
        # Check that other sections are preserved
        assert "Role: Data Scientist" in result
        assert "Background: PhD in Statistics" in result

    def test_replace_topics_in_persona_no_topics_no_simulating_style(self, default_settings):
        """Test topic replacement when persona has neither topics nor simulating style."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        original_persona = """Role: DevOps Engineer

Background: Infrastructure specialist with cloud expertise"""

        new_topic = "Kubernetes orchestration and management"

        result = generator.replace_topics_in_persona(original_persona, new_topic)

        # Check that topics section was added at the end
        assert "Topics of interest:" in result
        assert new_topic in result
        # Check that other sections are preserved
        assert "Role: DevOps Engineer" in result
        assert "Background: Infrastructure specialist" in result

    def test_replace_topics_in_persona_with_examples_header(self, default_settings):
        """Test topic replacement with 'Topics of interest (with examples)' header."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        original_persona = """Role: Mobile Developer

Topics of interest (with examples):
- iOS development: Swift, UIKit, SwiftUI
- Android development: Kotlin, Jetpack Compose
- Cross-platform: React Native, Flutter

Simulating Style:
Technical and detail-oriented"""

        new_topic = "Progressive web app development"

        result = generator.replace_topics_in_persona(original_persona, new_topic)

        # Check that new topic replaced old ones
        assert new_topic in result
        assert "iOS development: Swift, UIKit, SwiftUI" not in result
        assert "Android development: Kotlin, Jetpack Compose" not in result
        assert "Cross-platform: React Native, Flutter" not in result
        # Check that other sections are preserved
        assert "Role: Mobile Developer" in result
        assert "Simulating Style:" in result

    def test_parse_batch_response_valid_json(self, default_settings):
        """Test parsing valid batch response with JSON."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        response = """
        SECTION 1 TOPICS:
        ["Python programming", "Django framework", "Web development"]
        
        SECTION 2 TOPICS:
        ["JavaScript", "React", "Frontend development"]
        """
        
        result = generator._parse_batch_response(response)
        
        assert len(result) == 2
        assert result[0] == ["Python programming", "Django framework", "Web development"]
        assert result[1] == ["JavaScript", "React", "Frontend development"]

    def test_parse_batch_response_invalid_json(self, default_settings):
        """Test parsing batch response with invalid JSON."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        response = """
        SECTION 1 TOPICS:
        [invalid json content
        
        SECTION 2 TOPICS:
        ["JavaScript", "React"]
        """
        
        result = generator._parse_batch_response(response)

        # The parser is more permissive and may still parse the valid section
        assert len(result) >= 1
        # Check that at least one valid section was parsed
        assert ["JavaScript", "React"] in result

    def test_parse_batch_response_no_sections(self, default_settings):
        """Test parsing batch response with no valid sections."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        response = "No valid sections here"
        
        result = generator._parse_batch_response(response)
        assert result == []

    @pytest.mark.asyncio
    async def test_extract_topics_batch_success(self, default_settings):
        """Test successful topic extraction from batch."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        topic_sections = [
            "- Python programming\n- Web development",
            "- JavaScript\n- React development"
        ]
        
        mock_response = """
        SECTION 1 TOPICS:
        ["Python programming", "Web development"]
        
        SECTION 2 TOPICS:
        ["JavaScript", "React development"]
        """
        
        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                
                result = await generator._extract_topics_batch(topic_sections)
                
                assert len(result) == 2
                assert result[0] == ["Python programming", "Web development"]
                assert result[1] == ["JavaScript", "React development"]

    @pytest.mark.asyncio
    async def test_deduplicate_topics_success(self, default_settings):
        """Test successful topic deduplication."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        topics = ["Python programming", "python development", "Web development", "web dev"]
        
        mock_response = '```json\n["Python programming", "Web development"]\n```'
        
        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                
                result = await generator._deduplicate_topics(topics)
                
                assert result == ["Python programming", "Web development"]

    @pytest.mark.asyncio
    async def test_deduplicate_topics_invalid_json(self, default_settings):
        """Test topic deduplication with invalid JSON response."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        topics = ["Python programming", "python development", "Web development"]
        
        mock_response = "Invalid JSON response"
        
        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                
                result = await generator._deduplicate_topics(topics)
                
                # Should fallback to basic deduplication
                assert isinstance(result, list)
                assert len(result) <= len(topics)

    @pytest.mark.asyncio
    async def test_generate_persona_from_topic(self, default_settings):
        """Test persona generation from topic."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)
        
        mock_persona = "Generated persona for Python development"
        
        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, return_value=mock_persona):
                
                result = await generator._generate_persona_from_topic(
                    "developer", "Python programming", "mock knowledge graph"
                )
                
                assert result == mock_persona

    @pytest.mark.asyncio
    async def test_generate_topics_for_new_personas_success(self, default_settings):
        """Test successful generation of new topics."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        mock_response = '```json\n["Machine Learning", "Data Science", "AI Development"]\n```'

        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, return_value=mock_response):

                result = await generator._generate_topics_for_new_personas(
                    "data scientist", "mock knowledge graph", 3, ["Python programming"]
                )

                assert result == ["Machine Learning", "Data Science", "AI Development"]

    @pytest.mark.asyncio
    async def test_generate_topics_for_new_personas_invalid_json(self, default_settings):
        """Test new topic generation with invalid JSON response."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        mock_response = "Invalid JSON response"

        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, return_value=mock_response):

                result = await generator._generate_topics_for_new_personas(
                    "data scientist", "mock knowledge graph", 3, ["Python programming"]
                )

                # Should return default topic on error
                assert result == ["General knowledge about data scientist"]

    @pytest.mark.asyncio
    async def test_extract_topics_from_existing_personas_empty_list(self, default_settings):
        """Test topic extraction with empty personas list."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        result = await generator._extract_topics_from_existing_personas([], "mock knowledge graph")
        assert result == []

    @pytest.mark.asyncio
    async def test_extract_topics_from_existing_personas_no_topic_sections(self, default_settings):
        """Test topic extraction when personas have no topic sections."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        personas = [
            "Role: Developer\nBackground: Experienced",
            "Role: Designer\nBackground: Creative"
        ]

        result = await generator._extract_topics_from_existing_personas(personas, "mock knowledge graph")
        assert result == []

    @pytest.mark.asyncio
    async def test_extract_topics_from_existing_personas_success(self, default_settings, sample_personas):
        """Test successful topic extraction from existing personas."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        # Mock the batch extraction and deduplication
        mock_batch_response = [
            ["Web frameworks", "Database integration", "API development"],
            ["JavaScript frameworks", "CSS styling", "User experience"]
        ]

        mock_deduplicated = ["Web frameworks", "Database integration", "API development", "JavaScript frameworks", "CSS styling"]

        with patch.object(generator, '_extract_topics_batch', new_callable=AsyncMock, return_value=mock_batch_response):
            with patch.object(generator, '_deduplicate_topics', new_callable=AsyncMock, return_value=mock_deduplicated):
                with patch('src.persona_topic.knowledge_graph_system_prompt.generator.batch_gather', new_callable=AsyncMock, return_value=[mock_batch_response]):

                    result = await generator._extract_topics_from_existing_personas(sample_personas, "mock knowledge graph")

                    assert result == mock_deduplicated
                    assert len(result) == 5

    @pytest.mark.asyncio
    async def test_generate_main_function_success(self, default_settings, sample_knowledge_graph, sample_personas):
        """Test the main generate function with successful execution using existing personas."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        # Mock the subgraph creation
        mock_subgraph = SubGraph(
            entities=[Entity(id="Python", type="language")],
            relationships=[],
            focus_entities=["Python"],
            metadata={}
        )

        # Mock gap filling result
        mock_gap_filling_result = GapFillingResult(
            new_topics=["Machine Learning", "Data Science"],
            islands=[],
            total_new_topics=2,
            total_islands=0,
            total_core_nodes=0
        )

        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.create_subgraph', new_callable=AsyncMock, return_value=mock_subgraph):
            with patch.object(generator, '_extract_topics_from_existing_personas', new_callable=AsyncMock, return_value=["Python programming"]):
                with patch('src.persona_topic.knowledge_graph_system_prompt.generator.generate_gap_filling_topics', new_callable=AsyncMock, return_value=mock_gap_filling_result):
                    with patch('random.choice', side_effect=sample_personas):  # Mock random.choice to return predictable results

                        result = await generator.generate(
                            role="data scientist",
                            experiment_intent="test experiment",
                            max_personas=2,
                            knowledge_graph=sample_knowledge_graph,
                            existing_personas=sample_personas
                        )

                        assert len(result) == 2
                        assert all(isinstance(pt, PersonaTopic) for pt in result)
                        assert result[0].topic == "Machine Learning"
                        assert result[1].topic == "Data Science"
                        assert all(pt.generation_source == DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT for pt in result)

                        # Verify that personas were modified, not generated from scratch
                        assert "Machine Learning" in result[0].persona
                        assert "Data Science" in result[1].persona
                        # Should not contain old topics from sample personas
                        assert "Web frameworks like Django and Flask" not in result[0].persona
                        assert "JavaScript frameworks and libraries" not in result[1].persona

    @pytest.mark.asyncio
    async def test_generate_no_existing_personas(self, default_settings, sample_knowledge_graph):
        """Test generate function with no existing personas."""
        generator = KnowledgeGraphSystemPromptGenerator(default_settings)

        # Mock the subgraph creation
        mock_subgraph = SubGraph(
            entities=[Entity(id="Python", type="language")],
            relationships=[],
            focus_entities=["Python"],
            metadata={}
        )

        # Mock gap filling result
        mock_gap_filling_result = GapFillingResult(
            new_topics=["AI Development"],
            islands=[],
            total_new_topics=1,
            total_islands=0,
            total_core_nodes=0
        )

        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.create_subgraph', new_callable=AsyncMock, return_value=mock_subgraph):
            with patch.object(generator, '_extract_topics_from_existing_personas', new_callable=AsyncMock, return_value=[]):
                with patch('src.persona_topic.knowledge_graph_system_prompt.generator.generate_gap_filling_topics', new_callable=AsyncMock, return_value=mock_gap_filling_result):

                    result = await generator.generate(
                        role="developer",
                        experiment_intent="test experiment",
                        max_personas=1,
                        knowledge_graph=sample_knowledge_graph
                    )

                    assert len(result) == 1
                    assert result[0].topic == "AI Development"
                    # Should create a simple persona template when no existing personas
                    assert "Role: developer" in result[0].persona
                    assert "AI Development" in result[0].persona
                    assert "Topics of interest:" in result[0].persona
                    assert "Simulating Style:" in result[0].persona

    def test_summarize(self, default_settings):
        """Test the summarize method."""
        metadata = {"experiment_id": "test_123"}
        generator = KnowledgeGraphSystemPromptGenerator(default_settings, metadata)

        summary = generator.summarize()

        assert summary["type"] == "knowledge_graph_system_prompt"
        assert summary["settings"] == default_settings.model_dump()
        assert summary["metadata"] == metadata


class TestKnowledgeGraphGeneratorEdgeCases:
    """Test edge cases and error handling for the generator."""

    @pytest.fixture
    def generator(self):
        """Create a generator with default settings."""
        settings = KnowledgeGraphSystemPromptGeneratorSettings()
        return KnowledgeGraphSystemPromptGenerator(settings)

    def test_replace_topics_edge_cases(self, generator):
        """Test topic replacement with various edge cases."""
        # Test with empty persona
        result = generator.replace_topics_in_persona("", "New topic")
        assert "Topics of interest:" in result
        assert "New topic" in result

        # Test with persona containing only whitespace
        result = generator.replace_topics_in_persona("   \n  \t  ", "New topic")
        assert "Topics of interest:" in result
        assert "New topic" in result

        # Test with multiple "Topics of interest" sections (should replace everything between first one and Simulating Style)
        persona_multiple = """Role: Developer

Topics of interest:
- First topic section
- Another topic

Some content here

Topics of interest:
- Second topic section

Simulating Style:
Professional"""

        result = generator.replace_topics_in_persona(persona_multiple, "Replacement topic")
        assert "Replacement topic" in result
        assert "First topic section" not in result
        # Everything between first "Topics of interest" and "Simulating Style" should be replaced
        assert "Second topic section" not in result
        assert "Some content here" not in result
        assert "Role: Developer" in result
        assert "Simulating Style:" in result

        # Test with topics section that has no actual topics
        persona_empty_topics = """Role: Developer

Topics of interest:

Simulating Style:
Professional"""

        result = generator.replace_topics_in_persona(persona_empty_topics, "New topic")
        assert "New topic" in result
        assert "Role: Developer" in result
        assert "Simulating Style:" in result

    def test_replace_topics_preserves_formatting(self, generator):
        """Test that topic replacement preserves the overall formatting."""
        persona_with_formatting = """Role: Senior Software Engineer

Background:
- 10+ years of experience
- Expert in multiple programming languages
- Team lead experience

Professional Experience:
Currently working at a Fortune 500 company as a technical lead.

Topics of interest:
- Microservices architecture
- Cloud computing platforms
- DevOps practices and tools

Simulating Style:
Analytical and methodical approach to problem-solving"""

        result = generator.replace_topics_in_persona(persona_with_formatting, "Artificial intelligence and machine learning")

        # Check that the new topic is present
        assert "Artificial intelligence and machine learning" in result
        # Check that old topics are gone
        assert "Microservices architecture" not in result
        assert "Cloud computing platforms" not in result
        assert "DevOps practices and tools" not in result
        # Check that all other sections are preserved
        assert "Role: Senior Software Engineer" in result
        assert "Background:" in result
        assert "10+ years of experience" in result
        assert "Professional Experience:" in result
        assert "Fortune 500 company" in result
        assert "Simulating Style:" in result
        assert "Analytical and methodical" in result

    @pytest.mark.asyncio
    async def test_extract_topics_batch_llm_error(self, generator):
        """Test topic extraction when LLM fails."""
        topic_sections = ["- Python programming"]

        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, side_effect=Exception("LLM Error")):

                # Should raise the exception
                with pytest.raises(Exception, match="LLM Error"):
                    await generator._extract_topics_batch(topic_sections)

    @pytest.mark.asyncio
    async def test_deduplicate_topics_llm_error(self, generator):
        """Test topic deduplication when LLM fails."""
        topics = ["Python programming", "python development"]

        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, side_effect=Exception("LLM Error")):

                # Should raise the exception
                with pytest.raises(Exception, match="LLM Error"):
                    await generator._deduplicate_topics(topics)

    @pytest.mark.asyncio
    async def test_generate_persona_from_topic_llm_error(self, generator):
        """Test persona generation when LLM fails."""
        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, side_effect=Exception("LLM Error")):

                # Should raise the exception
                with pytest.raises(Exception, match="LLM Error"):
                    await generator._generate_persona_from_topic("role", "topic", "kg")

    @pytest.mark.asyncio
    async def test_generate_topics_for_new_personas_llm_error(self, generator):
        """Test new topic generation when LLM fails."""
        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.generator.achat_completion', new_callable=AsyncMock, side_effect=Exception("LLM Error")):

                # Should raise the exception
                with pytest.raises(Exception, match="LLM Error"):
                    await generator._generate_topics_for_new_personas("role", "kg", 3, [])

    def test_extract_topics_section_edge_cases(self, generator):
        """Test topic section extraction with various edge cases."""
        # Test with multiple "Topics of interest" sections
        persona_multiple = """
        Topics of interest:
        - First section

        Some other content

        Topics of interest:
        - Second section

        Simulating Style:
        Professional
        """

        result = generator.extract_topics_section(persona_multiple)
        # Should extract from the first occurrence
        assert "First section" in result

        # Test with empty topics section
        persona_empty = """
        Role: Developer

        Topics of interest:

        Simulating Style:
        Professional
        """

        result = generator.extract_topics_section(persona_empty)
        # The function removes the header, so empty section returns empty string
        assert result == ""
        # But should not have actual topic content
        assert "- " not in result

        # Test with topics at the very end
        persona_end = """
        Role: Developer

        Topics of interest:
        - Final topic
        """

        result = generator.extract_topics_section(persona_end)
        assert "Final topic" in result

    def test_parse_batch_response_edge_cases(self, generator):
        """Test batch response parsing with edge cases."""
        # Test with malformed section headers
        response_malformed = """
        SECTION TOPICS:  # Missing number
        ["topic1"]

        SECTION 2:  # Missing TOPICS
        ["topic2"]

        SECTION 3 TOPICS:
        ["topic3"]
        """

        result = generator._parse_batch_response(response_malformed)
        # The parser is more permissive than expected, it parses sections with partial matches
        assert len(result) >= 1
        # Check that valid topics are included
        assert ["topic3"] in result

        # Test with empty sections
        response_empty = """
        SECTION 1 TOPICS:

        SECTION 2 TOPICS:
        []
        """

        result = generator._parse_batch_response(response_empty)
        assert len(result) == 1
        assert result[0] == []

        # Test with non-JSON content
        response_non_json = """
        SECTION 1 TOPICS:
        This is not JSON content
        - topic1
        - topic2
        """

        result = generator._parse_batch_response(response_non_json)
        # When JSON parsing fails completely, the function may return empty list
        assert len(result) >= 0  # Could be empty if no valid JSON is found

    @pytest.mark.asyncio
    async def test_extract_topics_from_existing_personas_batch_processing(self, generator):
        """Test topic extraction with large number of personas requiring batching."""
        # Create 12 personas to test batching (batch size is 5)
        personas = []
        for i in range(12):
            personas.append(f"""
            Role: Developer {i}

            Topics of interest:
            - Topic {i}A
            - Topic {i}B

            Simulating Style:
            Professional
            """)

        # Mock batch responses
        mock_batch_responses = [
            [["Topic 0A", "Topic 0B"], ["Topic 1A", "Topic 1B"], ["Topic 2A", "Topic 2B"], ["Topic 3A", "Topic 3B"], ["Topic 4A", "Topic 4B"]],
            [["Topic 5A", "Topic 5B"], ["Topic 6A", "Topic 6B"], ["Topic 7A", "Topic 7B"], ["Topic 8A", "Topic 8B"], ["Topic 9A", "Topic 9B"]],
            [["Topic 10A", "Topic 10B"], ["Topic 11A", "Topic 11B"]]
        ]

        mock_deduplicated = ["Topic 0A", "Topic 1A", "Topic 2A"]  # Simulated deduplication result

        with patch.object(generator, '_extract_topics_batch', new_callable=AsyncMock, side_effect=mock_batch_responses):
            with patch.object(generator, '_deduplicate_topics', new_callable=AsyncMock, return_value=mock_deduplicated):
                with patch('src.persona_topic.knowledge_graph_system_prompt.generator.batch_gather', new_callable=AsyncMock, return_value=mock_batch_responses):

                    result = await generator._extract_topics_from_existing_personas(personas, "mock knowledge graph")

                    assert result == mock_deduplicated
                    # Verify that _extract_topics_batch was called 3 times (for 3 batches)
                    assert generator._extract_topics_batch.call_count == 3

    @pytest.mark.asyncio
    async def test_generate_with_empty_gap_filling_result(self, generator):
        """Test generate function when gap filling returns no topics."""
        sample_kg = KnowledgeGraph(entities=[], relationships=[], metadata={})

        # Mock empty gap filling result
        mock_gap_filling_result = GapFillingResult(
            new_topics=[],
            islands=[],
            total_new_topics=0,
            total_islands=0,
            total_core_nodes=0
        )

        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.create_subgraph', new_callable=AsyncMock):
            with patch.object(generator, '_extract_topics_from_existing_personas', new_callable=AsyncMock, return_value=[]):
                with patch('src.persona_topic.knowledge_graph_system_prompt.generator.generate_gap_filling_topics', new_callable=AsyncMock, return_value=mock_gap_filling_result):

                    result = await generator.generate(
                        role="developer",
                        experiment_intent="test",
                        max_personas=5,
                        knowledge_graph=sample_kg
                    )

                    assert result == []

    @pytest.mark.asyncio
    async def test_generate_random_sampling_behavior(self, generator):
        """Test that generate function randomly samples from existing personas."""
        sample_kg = KnowledgeGraph(entities=[], relationships=[], metadata={})

        existing_personas = [
            """Role: Backend Developer
Topics of interest:
- API design
Simulating Style: Technical""",
            """Role: Frontend Developer
Topics of interest:
- UI/UX design
Simulating Style: Creative""",
            """Role: DevOps Engineer
Topics of interest:
- Infrastructure
Simulating Style: Systematic"""
        ]

        # Mock gap filling result with multiple topics
        mock_gap_filling_result = GapFillingResult(
            new_topics=["Topic A", "Topic B", "Topic C"],
            islands=[],
            total_new_topics=3,
            total_islands=0,
            total_core_nodes=0
        )

        # Mock random.choice to return specific personas in order
        mock_choices = [existing_personas[0], existing_personas[1], existing_personas[2]]

        with patch('src.persona_topic.knowledge_graph_system_prompt.generator.create_subgraph', new_callable=AsyncMock):
            with patch.object(generator, '_extract_topics_from_existing_personas', new_callable=AsyncMock, return_value=[]):
                with patch('src.persona_topic.knowledge_graph_system_prompt.generator.generate_gap_filling_topics', new_callable=AsyncMock, return_value=mock_gap_filling_result):
                    with patch('random.choice', side_effect=mock_choices):

                        result = await generator.generate(
                            role="developer",
                            experiment_intent="test",
                            max_personas=3,
                            knowledge_graph=sample_kg,
                            existing_personas=existing_personas
                        )

                        assert len(result) == 3

                        # Verify that each result uses a different base persona
                        assert "Backend Developer" in result[0].persona
                        assert "Frontend Developer" in result[1].persona
                        assert "DevOps Engineer" in result[2].persona

                        # Verify that topics were replaced correctly
                        assert "Topic A" in result[0].persona
                        assert "Topic B" in result[1].persona
                        assert "Topic C" in result[2].persona

                        # Verify old topics were removed
                        assert "API design" not in result[0].persona
                        assert "UI/UX design" not in result[1].persona
                        assert "Infrastructure" not in result[2].persona


