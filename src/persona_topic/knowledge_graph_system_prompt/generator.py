import json
import async<PERSON>
import random
from typing import Dict, List, Optional, Any
from utils.logger import logger

from ..models import DataGenSource, PersonaTopic
from ..persona_topic_generator import PersonaTopicGeneratorInterface
from ..utils import format_prompt_from_template, achat_completion, batch_gather
from ..constants import GPT_4_1_MINI
from .settings import KnowledgeGraphSystemPromptGeneratorSettings
from .knowledge_graph import KnowledgeGraph
from .subgraph_selection import create_subgraph, SubGraph
from .gap_filling import generate_gap_filling_topics, GapFillingResult


class KnowledgeGraphSystemPromptGenerator(PersonaTopicGeneratorInterface):
    """
    Generator that creates personas and topics based on knowledge graph system prompts.
    This is a specialized version of the knowledge base generator that works specifically
    with knowledge graph data in system prompts.
    """

    def __init__(
        self,
        settings: KnowledgeGraphSystemPromptGeneratorSettings,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the Knowledge Graph System Prompt Generator.

        Args:
            settings: Settings for the Knowledge Graph System Prompt Generator
            metadata: Additional metadata to include in the generated personas/topics
        """
        super().__init__(metadata=metadata)
        self.settings = settings
        self.metadata = metadata or {}
        
        # Setup completion parameters
        self.completion_params = {
            "temperature": self.settings.temperature,
            "max_tokens": self.settings.max_tokens,
        }
        
        logger.info(f"Initialized KnowledgeGraphSystemPromptGenerator with settings: {settings}")

    def extract_topics_section(self, persona: str) -> str:
        """Extract the 'Topics of interest' section from a persona."""
        # Check for both "Topics of interest" and "Topics of Interest" (case variations)
        topics_patterns = ["Topics of interest", "Topics of Interest"]
        topics_start = -1

        for pattern in topics_patterns:
            pos = persona.find(pattern)
            if pos != -1:
                topics_start = pos
                break

        if topics_start == -1:
            return ""

        topics_end = persona.find("Simulating Style")

        if topics_end == -1:  # If "Simulating Style" not found, go to end
            topics_section = persona[topics_start:]
        else:
            topics_section = persona[topics_start:topics_end].strip()

        # Clean up the section headers
        if "Topics of interest (with examples):" in topics_section:
            topics_section = topics_section.replace("Topics of interest (with examples):", "").strip()
        if "Topics of Interest:" in topics_section:
            topics_section = topics_section.replace("Topics of Interest:", "").strip()
        if "Topics of interest:" in topics_section:
            topics_section = topics_section.replace("Topics of interest:", "").strip()

        return topics_section

    def replace_topics_in_persona(self, persona: str, new_topic: str) -> str:
        """Replace the topics section in a persona with a new topic."""
        # Check for both "Topics of interest" and "Topics of Interest" (case variations)
        topics_patterns = ["Topics of interest", "Topics of Interest"]
        topics_start = -1
        topics_pattern = None

        for pattern in topics_patterns:
            pos = persona.find(pattern)
            if pos != -1:
                topics_start = pos
                topics_pattern = pattern
                break

        if topics_start == -1:
            # If no topics section exists, add one before "Simulating Style"
            if "Simulating Style" in persona:
                simulating_start = persona.find("Simulating Style")
                before_simulating = persona[:simulating_start].rstrip()
                after_simulating = persona[simulating_start:]
                return f"{before_simulating}\n\nTopics of interest:\n- {new_topic}\n\n{after_simulating}"
            else:
                # Add at the end
                return f"{persona.rstrip()}\n\nTopics of interest:\n- {new_topic}"

        # Find the topics section boundaries
        topics_end = persona.find("Simulating Style")

        if topics_end == -1:  # If "Simulating Style" not found, replace to end
            before_topics = persona[:topics_start]
            return f"{before_topics}Topics of interest:\n- {new_topic}"
        else:
            before_topics = persona[:topics_start]
            after_topics = persona[topics_end:]
            return f"{before_topics}Topics of interest:\n- {new_topic}\n\n{after_topics}"

    async def _extract_topics_batch(self, topic_sections: List[str], knowledge_graph: str = None):
        """Extract topics from a batch of topic sections using LLM."""
        prompt = format_prompt_from_template(
            "knowledge_graph/extract_topics.jinja2",
            topic_sections=topic_sections,
            knowledge_graph=knowledge_graph
        )
        
        text = await achat_completion(
            model=self.settings.model or GPT_4_1_MINI,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
            **self.completion_params
        )
        
        return self._parse_batch_response(text)
        
    def _parse_batch_response(self, response_text: str):
        """Parse the LLM's response to extract topics for each section."""
        all_topics = []
        current_section = None
        topic_text = ""
        
        for line in response_text.split("\n"):
            if "SECTION" in line and "TOPICS" in line:
                # If we were collecting topics for a previous section, save them
                if current_section is not None and topic_text:
                    try:
                        # Clean up and parse the JSON
                        topic_text = topic_text.strip()
                        if topic_text.startswith("[") and topic_text.endswith("]"):
                            section_topics = json.loads(topic_text)
                            all_topics.append(section_topics)
                    except json.JSONDecodeError:
                        logger.error(f"Error parsing topics for section {current_section}:\n{topic_text}")
                        all_topics.append([])
                
                # Start collecting for the new section
                current_section = line.split()[1]
                topic_text = ""
            elif current_section is not None:
                topic_text += line + "\n"
        
        # Don't forget the last section
        if current_section is not None and topic_text:
            try:
                topic_text = topic_text.strip()
                if topic_text.startswith("[") and topic_text.endswith("]"):
                    section_topics = json.loads(topic_text)
                    all_topics.append(section_topics)
            except json.JSONDecodeError:
                logger.error(f"Error parsing topics for section {current_section}:\n{topic_text}")
                all_topics.append([])
        
        return all_topics
        
    async def _deduplicate_topics(self, all_topics_flat: List[str]):
        """Use an LLM to deduplicate and refine the topics."""
        topics_json = json.dumps(all_topics_flat, indent=2)
        
        prompt = format_prompt_from_template(
            "knowledge_graph/deduplicate_topics.jinja2",
            topics_json=topics_json
        )
        
        text = await achat_completion(
            model=self.settings.model or GPT_4_1_MINI,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
            **self.completion_params
        )
        
        # Extract the JSON array from the response
        text = text.strip()
        if "```json" in text:
            text = text.split("```json")[1].split("```")[0].strip()
        elif "```" in text:
            text = text.split("```")[1].split("```")[0].strip()
        
        try:
            # Parse the JSON
            deduplicated_topics = json.loads(text)
            return deduplicated_topics
        except json.JSONDecodeError:
            logger.error(f"Error parsing deduplicated topics:\n{text}")
            # Fallback to basic deduplication
            return list(set([t.lower() for t in all_topics_flat]))

    async def _generate_persona_from_topic(self, role: str, topic: str, knowledge_graph: str) -> str:
        """Generate a persona based on a topic using the knowledge graph."""
        prompt = format_prompt_from_template(
            "knowledge_graph/generate_persona.jinja2",
            role=role,
            topic=topic,
            knowledge_graph=knowledge_graph
        )
        
        return await achat_completion(
            model=self.settings.model or GPT_4_1_MINI,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
            **self.completion_params
        )
    
    async def _generate_topics_for_new_personas(self, role: str, knowledge_graph: str, count: int, covered_topics: List[str]) -> List[str]:
        """Generate new topics not covered by existing personas."""
        prompt = format_prompt_from_template(
            "knowledge_graph/generate_topics.jinja2",
            role=role,
            knowledge_graph=knowledge_graph,
            count=count,
            covered_topics=covered_topics
        )
        
        text = await achat_completion(
            model=self.settings.model or GPT_4_1_MINI,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
            **self.completion_params
        )
        
        # Extract the JSON array from the response
        text = text.strip()
        if "```json" in text:
            text = text.split("```json")[1].split("```")[0].strip()
        elif "```" in text:
            text = text.split("```")[1].split("```")[0].strip()
        
        try:
            # Parse the JSON
            new_topics = json.loads(text)
            return new_topics
        except json.JSONDecodeError:
            logger.error(f"Error parsing new topics:\n{text}")
            # Return a default topic if parsing fails
            return ["General knowledge about " + role]
            
    async def _extract_topics_from_existing_personas(self, existing_personas: List[str], knowledge_graph: str) -> List[str]:
        """
        Extract and deduplicate topics from existing personas.
        
        Args:
            existing_personas: List of existing persona descriptions
            knowledge_graph: Knowledge graph to use for context
            
        Returns:
            List of deduplicated topics
        """
        if not existing_personas:
            return []
            
        logger.info(f"Extracting topics from {len(existing_personas)} existing personas")
        
        # Extract topics sections from all personas
        topic_sections = []
        for persona in existing_personas:
            section = self.extract_topics_section(persona)
            if section:
                topic_sections.append(section)
        
        logger.info(f"Found {len(topic_sections)} topic sections from {len(existing_personas)} personas")
        
        if not topic_sections:
            return []
            
        # Process personas in batches
        PERSONAS_PER_BATCH = 5
        
        # Create extraction tasks for each batch of topic sections
        extraction_tasks = []
        for i in range(0, len(topic_sections), PERSONAS_PER_BATCH):
            batch = topic_sections[i:i+PERSONAS_PER_BATCH]
            task = self._extract_topics_batch(batch, knowledge_graph)
            extraction_tasks.append(task)
        
        # Execute all extraction tasks concurrently
        logger.info(f"Processing {len(extraction_tasks)} batches of topic sections")
        batch_results = await batch_gather(tasks=extraction_tasks, batch_size=3)
        
        # Collect and flatten all topics from batch results
        all_topics_batches = []
        for batch_topics in batch_results:
            batch_topics_flat = [topic for section_topics in batch_topics for topic in section_topics]
            all_topics_batches.append(batch_topics_flat)
        
        all_topics_flat = [topic for batch in all_topics_batches for topic in batch]
        logger.info(f"Collected {len(all_topics_flat)} raw topics before deduplication")
        
        # Deduplicate topics using LLM
        if not all_topics_flat:
            return []
            
        covered_topics = await self._deduplicate_topics(all_topics_flat)
        logger.info(f"Extracted {len(covered_topics)} unique topics after deduplication")
        return covered_topics
        
    async def generate(self, role: str, experiment_intent: str, max_personas: int, knowledge_graph: KnowledgeGraph, existing_personas: List[str] = None, **kwargs) -> List[PersonaTopic]:
        """
        Generate personas and topics based on knowledge graph system prompts.
        
        Args:
            role: The role or context for generating personas
            max_personas: Maximum number of personas to generate
            knowledge_graph: The knowledge graph content to use for generation
            existing_personas: Optional list of existing personas to extract topics from
            
        Returns:
            List of PersonaTopic objects
        """
        logger.info("Generating personas and topics from knowledge graph system prompts")
        
        # Create a subgraph based on the role
        # For now, we'll treat the role as both the experiment intent and product description
        product_description = role
        subgraph = await create_subgraph(knowledge_graph, experiment_intent, product_description)
        
        # Phase 1: Extract topics from existing personas
        covered_topics = await self._extract_topics_from_existing_personas(
            existing_personas or [], 
            knowledge_graph
        )

        gap_filling_result = await generate_gap_filling_topics(subgraph, covered_topics, k=max_personas)

        # Create PersonaTopic objects by replacing topics in existing personas
        persona_topics = []
        for topic in gap_filling_result.new_topics:
            if existing_personas:
                # Randomly sample an existing persona
                base_persona = random.choice(existing_personas)
                # Replace the topics section with the new topic
                modified_persona = self.replace_topics_in_persona(base_persona, topic)
            else:
                # If no existing personas, create a simple placeholder
                modified_persona = f"Role: {role}\n\nTopics of interest:\n- {topic}\n\nSimulating Style:\nProfessional and focused"

            persona_topics.append(
                PersonaTopic(
                    persona=modified_persona,
                    topic=topic,
                    generation_source=DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT
                )
            )

        return persona_topics

    def summarize(self) -> Dict[str, Any]:
        """
        Summarize the generator's settings and performance metrics.
        
        Returns:
            Dictionary with summary information
        """
        return {
            "type": "knowledge_graph_system_prompt",
            "settings": self.settings.model_dump(),
            "metadata": self.metadata,
        }