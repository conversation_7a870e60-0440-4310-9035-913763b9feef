from .models import DataGenSource
from .persona_topic_generator import PersonaTopicGeneratorInterface
from .app_description.generator import (
    AppDescriptionGenerator,
    AppDescriptionSystemPromptGenerator,
)
from .app_description.settings import (
    AppDescriptionGeneratorSettings,
    AppDescriptionSystemPromptGeneratorSettings,
)
from .risk_based.generator import RiskBasedGenerator
from .risk_based.settings import RiskBasedGeneratorSettings
from .historical_based.generator import HistoricalDataGenerator
from .historical_based.settings import HistoricalDataGeneratorSettings

from .knowledge_graph_system_prompt.generator import KnowledgeGraphSystemPromptGenerator
from .knowledge_graph_system_prompt.settings import KnowledgeGraphSystemPromptGeneratorSettings

from typing import Optional


class PersonaTopicGeneratorFactory:
    @staticmethod
    def get_historical_data_generator(
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the historical data generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        gen_settings = HistoricalDataGeneratorSettings(**settings)
        return HistoricalDataGenerator(gen_settings, metadata=metadata)

    @staticmethod
    def get_app_description_generator(
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the app description generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        gen_settings = AppDescriptionGeneratorSettings(**settings)
        return AppDescriptionGenerator(gen_settings, metadata=metadata)

    @staticmethod
    def get_risk_based_generator(
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the risk-based generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        gen_settings = RiskBasedGeneratorSettings(**settings)
        return RiskBasedGenerator(
            skip_list=[], settings=gen_settings, metadata=metadata
        )

    @staticmethod
    def get_app_description_system_prompt_generator(
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the app description system prompt generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        gen_settings = AppDescriptionSystemPromptGeneratorSettings(**settings)
        return AppDescriptionSystemPromptGenerator(gen_settings, metadata=metadata)
        
    @staticmethod
    def get_knowledge_graph_system_prompt_generator(
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the knowledge graph system prompt generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        gen_settings = KnowledgeGraphSystemPromptGeneratorSettings(**settings)
        return KnowledgeGraphSystemPromptGenerator(gen_settings, metadata=metadata)

    @staticmethod
    def create_generator(
        data_gen_source: DataGenSource,
        custom_generator_settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Create a generator based on the data generation source."""
        custom_generator_settings = custom_generator_settings or {}
        metadata = metadata or {}
        
        generator_mapping = {
            DataGenSource.HISTORICAL_DATA: PersonaTopicGeneratorFactory.get_historical_data_generator,
            DataGenSource.APP_DESCRIPTION: PersonaTopicGeneratorFactory.get_app_description_generator,
            DataGenSource.RISKS_SELECTED: PersonaTopicGeneratorFactory.get_risk_based_generator,
            DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT: PersonaTopicGeneratorFactory.get_app_description_system_prompt_generator,
            DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT: PersonaTopicGeneratorFactory.get_knowledge_graph_system_prompt_generator,
        }
        
        if data_gen_source not in generator_mapping:
            raise ValueError(f"Unknown data generation source: {data_gen_source}")
            
        generator_func = generator_mapping[data_gen_source]
        return generator_func(custom_generator_settings, metadata)
