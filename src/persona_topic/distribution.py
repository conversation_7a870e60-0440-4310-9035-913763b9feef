from abc import ABC, abstractmethod
from typing import Dict, List, Tuple
from .models import DataGenMode, DataGenSource
import math
import warnings
from utils.logger import logger


class DistributionStrategy(ABC):
    @abstractmethod
    def distribute(self, max_personas: int, **kwargs) -> List[Tuple[DataGenSource, int]]:
        """
        Distribute personas across different data sources.

        Returns:
            List of tuples (DataGenSource, count) in the order they should be processed.
            KNOWLEDGE_GRAPH_SYSTEM_PROMPT will always be last if present.
        """
        pass

    @staticmethod
    def _order_sources(distribution: Dict[DataGenSource, int]) -> List[Tuple[DataGenSource, int]]:
        """
        Order sources so that KNOWLEDGE_GRAPH_SYSTEM_PROMPT comes last.

        Args:
            distribution: Dictionary mapping DataGenSource to persona count

        Returns:
            List of tuples (DataGenSource, count) in processing order
        """
        ordered_sources = []
        kg_source_data = None

        # Separate KNOWLEDGE_GRAPH_SYSTEM_PROMPT to process it last
        for source, count in distribution.items():
            if count <= 0:
                continue

            if source == DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT:
                kg_source_data = (source, count)
            else:
                ordered_sources.append((source, count))

        # Add KNOWLEDGE_GRAPH_SYSTEM_PROMPT at the end if it exists
        if kg_source_data:
            ordered_sources.append(kg_source_data)

        return ordered_sources

    @staticmethod
    def log_stats(distribution) -> None:
        """
        Log statistics about the persona distribution strategy.

        Args:
            distribution: Either a dictionary mapping data generation sources to number of personas,
                         or a list of tuples (DataGenSource, count)
        """
        # Convert list of tuples to dictionary for backward compatibility
        if isinstance(distribution, list):
            distribution_dict = dict(distribution)
        else:
            distribution_dict = distribution

        max_personas_from_risks_selected = distribution_dict.get(
            DataGenSource.RISKS_SELECTED, 0
        )
        max_personas_from_app_description = distribution_dict.get(
            DataGenSource.APP_DESCRIPTION, 0
        )
        max_personas_from_app_description_system_prompt = distribution_dict.get(
            DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT, 0
        )
        max_personas_from_historical_data = distribution_dict.get(
            DataGenSource.HISTORICAL_DATA, 0
        )

        max_personas_from_knowledge_graph_system_prompt = distribution_dict.get(
            DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT, 0
        )

        # Log distribution information
        logger.info(f"Distribution: {distribution_dict}")
        logger.info(f"Personas from risks: {max_personas_from_risks_selected}")
        logger.info(
            f"Personas from app description: {max_personas_from_app_description}"
        )
        logger.info(
            f"Personas from app description system prompt: {max_personas_from_app_description_system_prompt}"
        )
        logger.info(
            f"Personas from knowledge graph system prompt: {max_personas_from_knowledge_graph_system_prompt}"
        )
        logger.info(
            f"Personas from historical data: {max_personas_from_historical_data}"
        )



class RiskFocusedDistribution(DistributionStrategy):
    def distribute(
        self,
        max_personas: int,
        **kwargs,
    ) -> List[Tuple[DataGenSource, int]]:
        num_risks: int = kwargs.get("num_risks", 0)
        historical_data_present: bool = kwargs.get("historical_data_present", False)
        knowledge_base_present: bool = kwargs.get("knowledge_base_present", False)
        data_gen_mode: DataGenMode = kwargs.get(
            "data_gen_mode", DataGenMode.RISK_FOCUSED
        )
        data_gen_mode_majority_fraction: float = kwargs.get(
            "data_gen_mode_majority_fraction", 0.75
        )
        # Allocate majority fraction to risks (or at least enough to cover all risks)
        risks_personas = math.ceil(
            max(max_personas * data_gen_mode_majority_fraction, num_risks)
        )

        app_description_source = (
            DataGenSource.APP_DESCRIPTION
            if data_gen_mode == DataGenMode.RISK_FOCUSED
            else DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT
        )

        # Remaining personas for coverage
        coverage_split = max(max_personas - risks_personas, 0)

        if coverage_split == 0:
            warnings.warn(
                f"Coverage split is 0. This is likely to result in low quality personas. Max personas: {max_personas}, "
                f"Number of risks: {num_risks}, Data gen mode majority fraction: {data_gen_mode_majority_fraction}"
            )
            return self._order_sources({DataGenSource.RISKS_SELECTED: risks_personas})

        # Distribute remaining personas based on available data sources
        result = {DataGenSource.RISKS_SELECTED: risks_personas}

        # Calculate weights for different data sources
        raw_weight_historical_data = 0.6 * int(historical_data_present)
        raw_weight_knowledge_base = 0.2 * int(knowledge_base_present)
        raw_weight_app_description = 0.2

        # Normalize weights
        total_weight = (
            raw_weight_historical_data
            + raw_weight_knowledge_base
            + raw_weight_app_description
        )
        if total_weight == 0:
            result[app_description_source] = coverage_split
            return self._order_sources(result)

        # Distribute coverage personas proportionally
        if historical_data_present:
            result[DataGenSource.HISTORICAL_DATA] = math.floor(
                coverage_split * raw_weight_historical_data / total_weight
            )
        if knowledge_base_present:
            # Always use KNOWLEDGE_GRAPH_SYSTEM_PROMPT for knowledge base data
            result[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] = math.floor(
                coverage_split * raw_weight_knowledge_base / total_weight
            )

        # Assign remaining to app description
        app_description_personas = coverage_split - sum(
            result.get(source, 0)
            for source in [DataGenSource.HISTORICAL_DATA, DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT]
        )
        result[app_description_source] = app_description_personas

        return self._order_sources(result)


class CoverageFocusedDistribution(DistributionStrategy):
    def distribute(
        self,
        max_personas: int,
        **kwargs,
    ) -> List[Tuple[DataGenSource, int]]:
        num_risks: int = kwargs.get("num_risks", 0)
        historical_data_present: bool = kwargs.get("historical_data_present", False)
        knowledge_base_present: bool = kwargs.get("knowledge_base_present", False)
        data_gen_mode: DataGenMode = kwargs.get(
            "data_gen_mode", DataGenMode.COVERAGE_FOCUSED
        )
        data_gen_mode_majority_fraction: float = kwargs.get(
            "data_gen_mode_majority_fraction", 0.9
        )
        # If risks exceed max personas, allocate all to risks
        if num_risks >= max_personas:
            return self._order_sources({DataGenSource.RISKS_SELECTED: max_personas})

        app_description_source = (
            DataGenSource.APP_DESCRIPTION
            if data_gen_mode == DataGenMode.COVERAGE_FOCUSED
            else DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT
        )

        # Allocate minimum number of personas to risks
        risks_personas = num_risks

        # Remaining personas for coverage
        coverage_split = max(max_personas - risks_personas, 0)

        if coverage_split == 0:
            warnings.warn(
                f"Coverage split is 0. This is likely to result in low quality personas. Max personas: {max_personas}, "
                f"Number of risks: {num_risks}, Data gen mode majority fraction: {data_gen_mode_majority_fraction}"
            )
            return self._order_sources({DataGenSource.RISKS_SELECTED: risks_personas})

        # Distribute remaining personas based on available data sources
        result = {DataGenSource.RISKS_SELECTED: risks_personas}

        # Calculate weights for different data sources
        raw_weight_historical_data = 0.6 * int(historical_data_present)
        raw_weight_knowledge_base = 0.2 * int(knowledge_base_present)
        raw_weight_app_description = 0.2

        # Normalize weights
        total_weight = (
            raw_weight_historical_data
            + raw_weight_knowledge_base
            + raw_weight_app_description
        )
        if total_weight == 0:
            result[app_description_source] = coverage_split
            return self._order_sources(result)

        # Distribute coverage personas proportionally
        if historical_data_present:
            result[DataGenSource.HISTORICAL_DATA] = math.floor(
                coverage_split * raw_weight_historical_data / total_weight
            )
        if knowledge_base_present:
            # Always use KNOWLEDGE_GRAPH_SYSTEM_PROMPT for knowledge base data
            result[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] = math.floor(
                coverage_split * raw_weight_knowledge_base / total_weight
            )

        # Assign remaining to app description
        app_description_personas = coverage_split - sum(
            result.get(source, 0)
            for source in [DataGenSource.HISTORICAL_DATA, DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT]
        )
        result[app_description_source] = app_description_personas

        return self._order_sources(result)





class CustomDistribution(DistributionStrategy):
    def distribute(self, max_personas: int, **kwargs) -> List[Tuple[DataGenSource, int]]:
        custom_generator_configuration = kwargs.get(
            "custom_generator_configuration", []
        )
        distribution = {}
        for data_gen_source in custom_generator_configuration:
            if "name" not in data_gen_source:
                logger.error("Invalid override configuration format. Missing 'name'.")
                continue

            name = data_gen_source["name"]
            settings = data_gen_source.get("settings", {})
            max_personas = settings.get("max_personas", 5)

            if max_personas > 0:
                # Assign the max_personas to the corresponding DataGenSource
                distribution[DataGenSource(name)] = max_personas
        # custom does not distribute personas, it returns values from override configuration directly
        return self._order_sources(distribution)


# Factory to get the right strategy
class DistributionStrategyFactory:
    @staticmethod
    def get_strategy(data_gen_mode: DataGenMode) -> DistributionStrategy:
        if (
            data_gen_mode == DataGenMode.RISK_FOCUSED
            or data_gen_mode == DataGenMode.RISK_FOCUSED_V3
        ):
            return RiskFocusedDistribution()
        elif (
            data_gen_mode == DataGenMode.COVERAGE_FOCUSED
            or data_gen_mode == DataGenMode.COVERAGE_FOCUSED_V3
        ):
            return CoverageFocusedDistribution()

        elif data_gen_mode == DataGenMode.CUSTOM:
            return CustomDistribution()
        else:
            raise ValueError(f"Unknown data generation mode: {data_gen_mode}")