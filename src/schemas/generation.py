from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional
from src.schemas.experiments import SourceData


@dataclass
class GenerateArgs:
    """Arguments for persona topic generation."""
    user_id: str
    role: str
    experiment_id: str
    source_data: SourceData
    user_description: str
    auth_header: Dict[str, str]
    dry_run: Optional[bool] = False


class GenerationMethods(str, Enum):
    persona = "persona"
    multiturn = "multiturn"
    manyturn = "manyturn"
    magpie = "magpie"
