from typing import Optional

from persona_topic import DataGenMode
from pydantic_settings import BaseSettings, SettingsConfigDict

ENV_VAR_TRUTHY_VALUES = ["true", "1", "t", "y", "yes"]


class PersonaTopicSettings(BaseSettings):
    topic_document_store: str = "s3"
    historical_document_store: str = "s3"
    aws_s3_document_bucket: str = "gr-threat-tester-demo-docs-bucket"
    max_personas: int = 10
    max_topics: Optional[int] = 10
    data_gen_mode: DataGenMode = DataGenMode.COVERAGE_FOCUSED
    data_gen_mode_majority_fraction: float = 0.90  # 90% of the conversations should be focused on the majority topics, where majority is either coverage focused or risk focused





class UserMessageSettings(BaseSettings):
    generation_method: Optional[str] = "manyturn"
    state_transition_method: Optional[str] = "random"


class Settings(BaseSettings):
    anthropic_api_key: Optional[str] = None
    openai_api_key: Optional[str] = None
    together_api_key: Optional[str] = None
    fireworks_ai_api_key: Optional[str] = None
    disable_auth: Optional[str] = "false"
    control_plane_url: Optional[str] = "http://localhost:8080"
    adaptability_level: Optional[int] = 1
    persona_topic_settings: PersonaTopicSettings = PersonaTopicSettings()
    user_message_settings: UserMessageSettings = UserMessageSettings()

    @property
    def disable_auth_bool(self):
        return (self.disable_auth or "").lower() in ENV_VAR_TRUTHY_VALUES

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


settings = Settings()
